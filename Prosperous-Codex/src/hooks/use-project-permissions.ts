"use client";

import { useMemo } from 'react';
import { Project, TeamMember } from '@/lib/types/task-master';

interface ProjectPermissions {
  // Task-related permissions
  canCreateTasks: boolean;
  canEditTasks: boolean;
  canDeleteTasks: boolean;
  canToggleTaskStatus: boolean;
  
  // Project-related permissions
  canEditProject: boolean;
  canEditEventLog: boolean;
  canUploadFiles: boolean;
  canDeleteFiles: boolean;
  
  // Team-related permissions
  canManageTeam: boolean;
  canInviteMembers: boolean;
  
  // Comment permissions (generally more permissive)
  canCreateComments: boolean;
  canEditOwnComments: boolean;
  canDeleteOwnComments: boolean;
  
  // Meta information
  isProjectOwner: boolean;
  isTeamMember: boolean;
  userRole: 'owner' | 'member' | 'viewer' | 'none';
}

interface UseProjectPermissionsProps {
  currentUserId?: string;
  project?: Project | null;
  teamMembers?: TeamMember[];
}

/**
 * Centralized hook for determining user permissions within a project context.
 * Provides granular permission checks for UI components to proactively disable
 * unauthorized actions instead of showing error messages after the fact.
 */
export function useProjectPermissions({
  currentUserId,
  project,
  teamMembers = []
}: UseProjectPermissionsProps): ProjectPermissions {
  
  return useMemo(() => {
    // Default permissions for unauthenticated users
    if (!currentUserId || !project) {
      return {
        canCreateTasks: false,
        canEditTasks: false,
        canDeleteTasks: false,
        canToggleTaskStatus: false,
        canEditProject: false,
        canEditEventLog: false,
        canUploadFiles: false,
        canDeleteFiles: false,
        canManageTeam: false,
        canInviteMembers: false,
        canCreateComments: true, // Comments are generally allowed for all users
        canEditOwnComments: true,
        canDeleteOwnComments: true,
        isProjectOwner: false,
        isTeamMember: false,
        userRole: 'none'
      };
    }

    // Check if user is project owner
    const isProjectOwner = project.createdBy?.toString() === currentUserId;
    
    // Check if user is team member
    const teamMember = teamMembers.find(member => 
      member.userId?.toString() === currentUserId
    );
    const isTeamMember = isProjectOwner || !!teamMember;
    
    // Determine user role within the project
    let userRole: 'owner' | 'member' | 'viewer' | 'none' = 'none';
    if (isProjectOwner) {
      userRole = 'owner';
    } else if (teamMember) {
      // Map team member roles to simplified permission roles
      switch (teamMember.role) {
        case 'admin':
        case 'moderator':
          userRole = 'member';
          break;
        case 'viewer':
        default:
          userRole = 'viewer';
          break;
      }
    }

    // Define permissions based on role
    const permissions: ProjectPermissions = {
      isProjectOwner,
      isTeamMember,
      userRole,
      
      // Comments are generally permissive
      canCreateComments: true,
      canEditOwnComments: true,
      canDeleteOwnComments: true,
      
      // Default all other permissions to false, then enable based on role
      canCreateTasks: false,
      canEditTasks: false,
      canDeleteTasks: false,
      canToggleTaskStatus: false,
      canEditProject: false,
      canEditEventLog: false,
      canUploadFiles: false,
      canDeleteFiles: false,
      canManageTeam: false,
      canInviteMembers: false
    };

    // Project owners have full permissions
    if (isProjectOwner) {
      permissions.canCreateTasks = true;
      permissions.canEditTasks = true;
      permissions.canDeleteTasks = true;
      permissions.canToggleTaskStatus = true;
      permissions.canEditProject = true;
      permissions.canEditEventLog = true;
      permissions.canUploadFiles = true;
      permissions.canDeleteFiles = true;
      permissions.canManageTeam = true;
      permissions.canInviteMembers = true;
    }
    // Team members with appropriate roles
    else if (teamMember) {
      switch (teamMember.role) {
        case 'admin':
          // Admin team members have most permissions except team management
          permissions.canCreateTasks = true;
          permissions.canEditTasks = true;
          permissions.canDeleteTasks = true;
          permissions.canToggleTaskStatus = true;
          permissions.canEditProject = true;
          permissions.canEditEventLog = true;
          permissions.canUploadFiles = true;
          permissions.canDeleteFiles = true;
          permissions.canInviteMembers = true;
          break;
          
        case 'moderator':
          // Moderators can edit content but not manage team
          permissions.canCreateTasks = true;
          permissions.canEditTasks = true;
          permissions.canDeleteTasks = true;
          permissions.canToggleTaskStatus = true;
          permissions.canEditEventLog = true;
          permissions.canUploadFiles = true;
          permissions.canDeleteFiles = true;
          break;
          
        case 'viewer':
        default:
          // Viewers have read-only access
          // All permissions remain false except comments
          break;
      }
    }

    return permissions;
  }, [currentUserId, project, teamMembers]);
}

/**
 * Helper function to get permission explanation text for disabled actions
 */
export function getPermissionExplanation(
  permission: keyof ProjectPermissions,
  userRole: 'owner' | 'member' | 'viewer' | 'none'
): string {
  const explanations: Record<string, Record<string, string>> = {
    canCreateTasks: {
      none: "You need to be a team member to create tasks",
      viewer: "Contact the project owner for editing permissions",
      member: "You have permission to create tasks",
      owner: "You have full permissions as project owner"
    },
    canEditTasks: {
      none: "You need to be a team member to edit tasks",
      viewer: "Contact the project owner for editing permissions", 
      member: "You have permission to edit tasks",
      owner: "You have full permissions as project owner"
    },
    canEditEventLog: {
      none: "You need to be a team member to edit the event log",
      viewer: "Contact the project owner for editing permissions",
      member: "You have permission to edit the event log", 
      owner: "You have full permissions as project owner"
    },
    canUploadFiles: {
      none: "You need to be a team member to upload files",
      viewer: "Contact the project owner for file upload permissions",
      member: "You have permission to upload files",
      owner: "You have full permissions as project owner"
    }
  };

  return explanations[permission]?.[userRole] || "Insufficient permissions";
}
