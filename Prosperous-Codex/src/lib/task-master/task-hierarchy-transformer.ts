/**
 * Task Hierarchy Transformation Utility
 * 
 * Handles conversion between flat task storage and hierarchical task display formats
 * following the established middleware translation layer patterns in the codebase.
 * 
 * This utility provides:
 * - Bidirectional transformation between flat and hierarchical task structures
 * - Type-safe transformations with proper error handling
 * - Consistent data structure management across storage and display layers
 * - Integration with existing field mapping conventions
 */

import { FieldMapper } from './field-mapping';

/**
 * Base task interface for transformations
 */
export interface BaseTask {
  id: string | number;
  title: string;
  description?: string;
  status: 'todo' | 'inProgress' | 'completed';
  priority: 'low' | 'medium' | 'high';
  parentTaskId?: string | number;
  projectId: string | number;
  createdAt: string;
  updatedAt: string;
  isTemporary?: boolean;
  [key: string]: any;
}

/**
 * Hierarchical task structure for display
 */
export interface HierarchicalTask extends Omit<BaseTask, 'parentTaskId'> {
  subtasks?: HierarchicalTask[];
}

/**
 * Transformation result with metadata
 */
export interface TransformationResult<T> {
  data: T;
  metadata: {
    totalTasks: number;
    mainTasks: number;
    subtasks: number;
    maxDepth: number;
    transformationType: 'flatToHierarchical' | 'hierarchicalToFlat';
  };
  warnings?: string[];
}

/**
 * Task Hierarchy Transformer Class
 * 
 * Provides static methods for converting between flat and hierarchical task structures
 * while maintaining data integrity and following established transformation patterns.
 */
export class TaskHierarchyTransformer {
  /**
   * Transform flat task array to hierarchical structure for display
   * 
   * @param flatTasks - Array of tasks with parentTaskId references
   * @returns Hierarchical structure with subtasks nested under parent tasks
   */
  static flatToHierarchical(flatTasks: BaseTask[]): TransformationResult<HierarchicalTask[]> {
    const warnings: string[] = [];
    
    // Separate main tasks and subtasks
    const mainTasks = flatTasks.filter(task => !task.parentTaskId);
    const subtasks = flatTasks.filter(task => task.parentTaskId);
    
    // Track orphaned subtasks (subtasks without valid parent)
    const orphanedSubtasks: BaseTask[] = [];
    
    // Build hierarchical structure
    const hierarchicalTasks: HierarchicalTask[] = mainTasks.map(mainTask => {
      const taskSubtasks = this.buildSubtaskHierarchy(mainTask.id, subtasks, orphanedSubtasks);
      
      // Remove parentTaskId from main task and add subtasks if any
      const { parentTaskId, ...hierarchicalTask } = mainTask;
      
      return {
        ...hierarchicalTask,
        ...(taskSubtasks.length > 0 && { subtasks: taskSubtasks })
      };
    });
    
    // Handle orphaned subtasks
    if (orphanedSubtasks.length > 0) {
      warnings.push(`Found ${orphanedSubtasks.length} orphaned subtasks without valid parent tasks`);
      
      // Convert orphaned subtasks to main tasks
      orphanedSubtasks.forEach(orphan => {
        const { parentTaskId, ...orphanAsMainTask } = orphan;
        hierarchicalTasks.push(orphanAsMainTask);
      });
    }
    
    // Calculate metadata
    const maxDepth = this.calculateMaxDepth(hierarchicalTasks);
    
    return {
      data: hierarchicalTasks,
      metadata: {
        totalTasks: flatTasks.length,
        mainTasks: mainTasks.length,
        subtasks: subtasks.length,
        maxDepth,
        transformationType: 'flatToHierarchical'
      },
      ...(warnings.length > 0 && { warnings })
    };
  }
  
  /**
   * Transform hierarchical task structure back to flat array for storage
   * 
   * @param hierarchicalTasks - Tasks with nested subtasks
   * @returns Flat array with parentTaskId references
   */
  static hierarchicalToFlat(hierarchicalTasks: HierarchicalTask[]): TransformationResult<BaseTask[]> {
    const flatTasks: BaseTask[] = [];
    
    const flattenTask = (task: HierarchicalTask, parentId?: string | number) => {
      // Add current task to flat array
      const flatTask: BaseTask = {
        ...task,
        ...(parentId && { parentTaskId: parentId })
      };
      
      // Remove subtasks property from flat task
      delete (flatTask as any).subtasks;
      
      flatTasks.push(flatTask);
      
      // Recursively flatten subtasks
      if (task.subtasks && task.subtasks.length > 0) {
        task.subtasks.forEach(subtask => {
          flattenTask(subtask, task.id);
        });
      }
    };
    
    // Flatten all hierarchical tasks
    hierarchicalTasks.forEach(task => flattenTask(task));
    
    // Calculate metadata
    const mainTasks = flatTasks.filter(task => !task.parentTaskId);
    const subtasks = flatTasks.filter(task => task.parentTaskId);
    
    return {
      data: flatTasks,
      metadata: {
        totalTasks: flatTasks.length,
        mainTasks: mainTasks.length,
        subtasks: subtasks.length,
        maxDepth: 0, // Not applicable for flat structure
        transformationType: 'hierarchicalToFlat'
      }
    };
  }
  
  /**
   * Build subtask hierarchy recursively
   */
  private static buildSubtaskHierarchy(
    parentId: string | number,
    allSubtasks: BaseTask[],
    orphanedSubtasks: BaseTask[]
  ): HierarchicalTask[] {
    const directSubtasks = allSubtasks.filter(subtask =>
      // Handle type mismatches between parent ID and subtask parentTaskId
      // Convert both to strings for comparison to handle mixed number/string IDs
      String(subtask.parentTaskId) === String(parentId)
    );
    
    return directSubtasks.map(subtask => {
      // Check if this subtask has its own subtasks
      const nestedSubtasks = this.buildSubtaskHierarchy(subtask.id, allSubtasks, orphanedSubtasks);
      
      // Remove parentTaskId and add nested subtasks if any
      const { parentTaskId, ...hierarchicalSubtask } = subtask;
      
      return {
        ...hierarchicalSubtask,
        ...(nestedSubtasks.length > 0 && { subtasks: nestedSubtasks })
      };
    });
  }
  
  /**
   * Calculate maximum depth of task hierarchy
   */
  private static calculateMaxDepth(tasks: HierarchicalTask[], currentDepth = 0): number {
    let maxDepth = currentDepth;
    
    tasks.forEach(task => {
      if (task.subtasks && task.subtasks.length > 0) {
        const subtaskDepth = this.calculateMaxDepth(task.subtasks, currentDepth + 1);
        maxDepth = Math.max(maxDepth, subtaskDepth);
      }
    });
    
    return maxDepth;
  }
  
  /**
   * Validate task structure integrity
   */
  static validateTaskStructure(tasks: BaseTask[]): {
    isValid: boolean;
    errors: string[];
    warnings: string[];
  } {
    const errors: string[] = [];
    const warnings: string[] = [];
    
    // Check for circular references
    const taskIds = new Set(tasks.map(task => task.id));
    
    tasks.forEach(task => {
      if (task.parentTaskId) {
        // Check if parent exists
        if (!taskIds.has(task.parentTaskId)) {
          warnings.push(`Task ${task.id} references non-existent parent ${task.parentTaskId}`);
        }
        
        // Check for self-reference
        if (task.parentTaskId === task.id) {
          errors.push(`Task ${task.id} cannot be its own parent`);
        }
      }
    });
    
    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }
}

/**
 * Utility functions for common task transformations
 */
export const TaskTransformationUtils = {
  /**
   * Quick flat to hierarchical transformation
   */
  toHierarchical: (flatTasks: BaseTask[]): HierarchicalTask[] => {
    const result = TaskHierarchyTransformer.flatToHierarchical(flatTasks);
    return result.data;
  },
  
  /**
   * Quick hierarchical to flat transformation
   */
  toFlat: (hierarchicalTasks: HierarchicalTask[]): BaseTask[] => {
    const result = TaskHierarchyTransformer.hierarchicalToFlat(hierarchicalTasks);
    return result.data;
  },
  
  /**
   * Get only main tasks from flat array
   */
  getMainTasks: (flatTasks: BaseTask[]): BaseTask[] => {
    return flatTasks.filter(task => !task.parentTaskId);
  },
  
  /**
   * Get subtasks for a specific parent
   */
  getSubtasks: (flatTasks: BaseTask[], parentId: string | number): BaseTask[] => {
    return flatTasks.filter(task => task.parentTaskId === parentId);
  },
  
  /**
   * Check if task has subtasks
   */
  hasSubtasks: (flatTasks: BaseTask[], taskId: string | number): boolean => {
    return flatTasks.some(task => task.parentTaskId === taskId);
  }
};

/**
 * Export the main transformer class as default
 */
export { TaskHierarchyTransformer as default };
